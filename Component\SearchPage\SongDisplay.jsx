/* eslint-disable keyword-spacing */
import React, { useState, useEffect } from 'react'
import { Dimensions, FlatList, View } from 'react-native'
import { EachSongCard } from '../Global/EachSongCard'
import { getSearchSongData } from '../../Api/Songs'
import { getTidalSearchSongData } from '../../Api/TidalAPI'
import { LoadingComponent } from '../Global/Loading'
import { PlainText } from '../Global/PlainText'
import { SmallText } from '../Global/SmallText'
import { preloadTopTidalSongs } from '../../Utils/TidalMusicHandler'

export default function SongDisplay({data, limit, Searchtext, source = 'saavn'}) {
  const [Data, setData] = useState(data)
  const totalPages = Math.ceil(Data?.data?.total ?? 1 / limit)
  const [Page, setPage] = useState(1)
  const [Loading, setLoading] = useState(false)

  // Update data when props change
  useEffect(() => {
    setData(data);

    // Preload top 3 Tidal songs for faster playback
    if (data?.data?.results && source === 'tidal') {
      const tidalSongs = data.data.results.filter(song => song.tidalUrl);
      if (tidalSongs.length > 0) {
        preloadTopTidalSongs(tidalSongs, 3);
      }
    }
  }, [data, source]);
  async function fetchSearchData(text,page){
   if (Page <= totalPages){
   if(Searchtext !== ""){
    try {
        setLoading(true)
        let fetchdata;
        if (source === 'tidal') {
          fetchdata = await getTidalSearchSongData(text,page,limit)
        } else {
          fetchdata = await getSearchSongData(text,page,limit)
        }
        // Check if fetchdata has valid structure
        if (fetchdata && fetchdata.data && fetchdata.data.results) {
          const temp = Data
          if (temp && temp.data && temp.data.results) {
            const finalData = [...temp.data.results, ...fetchdata.data.results]
            temp.data.results = finalData
            setData(temp)

            // Preload top 3 Tidal songs from new results for faster playback
            if (source === 'tidal' && fetchdata.data.results.length > 0) {
              const newTidalSongs = fetchdata.data.results.filter(song => song.tidalUrl);
              if (newTidalSongs.length > 0) {
                preloadTopTidalSongs(newTidalSongs, 3);
              }
            }
          }
        }
      } catch (e) {
        console.log(e);
      } finally {
        setLoading(false)
      }
   }
   }
  }
  const width = Dimensions.get("window").width

  function FormatArtist(data){
    let artist = ""
    data?.map((e,i)=>{
      if(i === data.length - 1){
        artist += e.name
      }else{
        artist += e.name + ", "
      }
    })
    return artist
  }
  return (
    <View>
      {Data?.data?.results?.length !== 0 && <FlatList 
        showsVerticalScrollIndicator={false} keyExtractor={(item, index) => String(index)} onEndReached={()=>{
        setTimeout(()=>{
          setPage(Page + 1)
          fetchSearchData(Searchtext, Page)
        },200)
      }} contentContainerStyle={{
        paddingBottom:220,
      }} data={[...Data?.data?.results ?? [], {LoadingComponent:true}]} renderItem={(item)=>{
        if(item.item.LoadingComponent  === true){
            return <LoadingComponent loading={Loading} height={100}/>
        }else{
            return <EachSongCard
              artistID={item.item?.primaryArtistsId || item.item?.primary_artists_id}
              language={item.item?.language}
              duration={item.item?.duration}
              image={item?.item?.image[2]?.url ?? item?.item?.image[0]?.url ?? ""}
              id={item.item?.id}
              width={width * 0.95}
              title={item.item?.name || item.item?.title}
              artist={source === 'tidal' ? item.item?.artist : FormatArtist(item.item?.artists?.primary)}
              url={item.item?.downloadUrl}
              showNumber={false}
              source={source}
              tidalUrl={item.item?.tidalUrl}
              getPosition={(event) => {
                const { pageY } = event.nativeEvent;
                return { y: pageY };
              }}
              style={{
                marginBottom: 13,
              }}
            />
          }
        }}
      />}
      {Data?.data?.results?.length === 0 && <View style={{
        height:400,
        alignItems:"center",
        justifyContent:"center",
      }}>
        <PlainText text={"No Song found!"}/>
        <SmallText text={"Opps!  T_T"}/>
        </View> }
     </View>
  )
}
