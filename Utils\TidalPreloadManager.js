import { getTidalStreamingUrl } from '../Api/TidalAPI';

/**
 * Intelligent Tidal preloading manager to prevent rate limiting
 * Only preloads top 3 songs and manages request queue
 */
class TidalPreloadManager {
  constructor() {
    this.preloadQueue = [];
    this.preloadedUrls = new Map(); // Cache preloaded URLs
    this.isProcessing = false;
    this.requestCount = 0;
    this.lastRequestTime = 0;
    this.maxRequestsPerMinute = 3; // Tidal API limit
    this.requestInterval = 60000; // 1 minute in milliseconds
    this.maxPreloadCount = 3; // Only preload top 3 songs
    this.currentPreloadCount = 0;
    
    // Proxy rotation for rate limit bypass
    this.proxyList = [
      null, // No proxy (default)
      // Add proxy servers here if available
      // 'http://proxy1:port',
      // 'http://proxy2:port',
    ];
    this.currentProxyIndex = 0;
  }

  /**
   * Add song to preload queue (only if within top 3)
   * @param {Object} tidalSong - Tidal song object
   * @param {number} priority - Priority (0 = highest, for top songs)
   */
  addToPreloadQueue(tidalSong, priority = 10) {
    // Only preload top 3 songs to respect rate limits
    if (this.currentPreloadCount >= this.maxPreloadCount) {
      return;
    }

    // Check if already preloaded or in queue
    if (this.preloadedUrls.has(tidalSong.tidalUrl)) {
      return;
    }

    // Check if already in queue
    const existingIndex = this.preloadQueue.findIndex(item => item.tidalUrl === tidalSong.tidalUrl);
    if (existingIndex !== -1) {
      return;
    }

    // Add to queue with priority
    this.preloadQueue.push({
      ...tidalSong,
      priority,
      addedAt: Date.now()
    });

    // Sort by priority (lower number = higher priority)
    this.preloadQueue.sort((a, b) => a.priority - b.priority);

    // Start processing if not already running
    if (!this.isProcessing) {
      this.processQueue();
    }
  }

  /**
   * Process the preload queue with rate limiting
   */
  async processQueue() {
    if (this.isProcessing || this.preloadQueue.length === 0) {
      return;
    }

    this.isProcessing = true;

    while (this.preloadQueue.length > 0 && this.currentPreloadCount < this.maxPreloadCount) {
      // Check rate limiting
      if (!this.canMakeRequest()) {
        const waitTime = this.getWaitTime();
        console.log(`Tidal rate limit: waiting ${waitTime}ms before next request`);
        await this.sleep(waitTime);
      }

      const song = this.preloadQueue.shift();
      
      try {
        await this.preloadSingleSong(song);
        this.currentPreloadCount++;
      } catch (error) {
        console.log(`Preload failed for ${song.title}:`, error.message);
        
        // If rate limited, wait longer and try again
        if (error.message.includes('429') || error.message.includes('rate limit')) {
          console.log('Rate limited, waiting 30 seconds before retry...');
          await this.sleep(30000); // Wait 30 seconds

          // Re-add to queue with same priority for retry
          this.preloadQueue.unshift(song);
          continue;
        }
      }

      // Small delay between requests
      await this.sleep(200);
    }

    this.isProcessing = false;
  }

  /**
   * Preload a single song's streaming URL
   * @param {Object} song - Song object
   */
  async preloadSingleSong(song) {
    try {
      console.log(`Preloading Tidal URL for: ${song.title}`);
      
      const streamingUrl = await getTidalStreamingUrl(song.tidalUrl, 'LOSSLESS');
      
      // Cache the preloaded URL
      this.preloadedUrls.set(song.tidalUrl, {
        url: streamingUrl,
        preloadedAt: Date.now(),
        expiresAt: Date.now() + (5 * 60 * 1000) // 5 minutes expiry
      });

      this.updateRequestCount();
      console.log(`Successfully preloaded: ${song.title}`);
      
    } catch (error) {
      this.updateRequestCount();
      throw error;
    }
  }

  /**
   * Get preloaded streaming URL if available
   * @param {string} tidalUrl - Tidal URL
   * @returns {string|null} Cached streaming URL or null
   */
  getPreloadedUrl(tidalUrl) {
    const cached = this.preloadedUrls.get(tidalUrl);
    
    if (!cached) {
      return null;
    }

    // Check if expired
    if (Date.now() > cached.expiresAt) {
      this.preloadedUrls.delete(tidalUrl);
      return null;
    }

    return cached.url;
  }

  /**
   * Check if we can make a request based on rate limiting
   */
  canMakeRequest() {
    const now = Date.now();
    
    // Reset counter if a minute has passed
    if (now - this.lastRequestTime > this.requestInterval) {
      this.requestCount = 0;
      this.lastRequestTime = now;
    }

    return this.requestCount < this.maxRequestsPerMinute;
  }

  /**
   * Calculate wait time until next request is allowed
   */
  getWaitTime() {
    const now = Date.now();
    const timeSinceLastReset = now - this.lastRequestTime;
    return Math.max(0, this.requestInterval - timeSinceLastReset);
  }

  /**
   * Update request count and timestamp
   */
  updateRequestCount() {
    this.requestCount++;
    this.lastRequestTime = Date.now();
  }

  /**
   * Rotate to next proxy to bypass rate limits
   */
  rotateProxy() {
    this.currentProxyIndex = (this.currentProxyIndex + 1) % this.proxyList.length;
    console.log(`Rotating to proxy index: ${this.currentProxyIndex}`);
  }

  /**
   * Get current proxy
   */
  getCurrentProxy() {
    return this.proxyList[this.currentProxyIndex];
  }

  /**
   * Sleep utility
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Clear all preloaded data
   */
  clearCache() {
    this.preloadedUrls.clear();
    this.preloadQueue = [];
    this.currentPreloadCount = 0;
    this.isProcessing = false;
  }

  /**
   * Reset preload count for new search results
   */
  resetForNewSearch() {
    this.currentPreloadCount = 0;
    this.preloadQueue = [];
    // Keep cached URLs as they might still be useful
  }

  /**
   * Get current status for debugging
   */
  getStatus() {
    return {
      preloadedCount: this.preloadedUrls.size,
      queueLength: this.preloadQueue.length,
      currentPreloadCount: this.currentPreloadCount,
      maxPreloadCount: this.maxPreloadCount,
      requestCount: this.requestCount,
      maxRequestsPerMinute: this.maxRequestsPerMinute,
      canMakeRequest: this.canMakeRequest(),
      isProcessing: this.isProcessing
    };
  }
}

// Create singleton instance
const tidalPreloadManager = new TidalPreloadManager();

export default tidalPreloadManager;
